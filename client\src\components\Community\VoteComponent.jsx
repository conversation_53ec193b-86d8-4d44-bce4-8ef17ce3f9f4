import React, { useState, useEffect, useRef } from 'react';
import {
  ChevronUp,
  ChevronDown,
  Shield,
  AlertTriangle,
  X
} from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import { useCounterAnimation, useHoverAnimation } from '../../hooks/useGSAP';
import { gsap } from '../../utils/gsap';

const VoteComponent = ({ linkId, postData, className = '' }) => {
  const { user } = useAuth();
  const { isDarkMode } = useTheme();
  const [voteStats, setVoteStats] = useState({
    safe: 0,
    unsafe: 0,
    suspicious: 0,
    total: 0
  });
  const [userVote, setUserVote] = useState(null); // 'safe', 'unsafe', 'suspicious', or null
  const [loading, setLoading] = useState(false);

  // GSAP refs and animations
  const [counterRef, startCounterAnimation] = useCounterAnimation(voteStats.total, {
    duration: 0.8,
    ease: "power2.out"
  });

  const safeButtonRef = useHoverAnimation(
    { scale: 1.1, duration: 0.2 },
    { scale: 1, duration: 0.2 }
  );

  const suspiciousButtonRef = useHoverAnimation(
    { scale: 1.1, duration: 0.2 },
    { scale: 1, duration: 0.2 }
  );

  const unsafeButtonRef = useHoverAnimation(
    { scale: 1.1, duration: 0.2 },
    { scale: 1, duration: 0.2 }
  );

  const loadingRef = useRef();

  // Load vote data on component mount
  useEffect(() => {
    if (postData && postData.voteStats) {
      // Use data from post
      const newStats = {
        safe: postData.voteStats.safe || 0,
        unsafe: postData.voteStats.unsafe || 0,
        suspicious: postData.voteStats.suspicious || 0,
        total: postData.voteStats.total || 0
      };
      setVoteStats(newStats);
      setUserVote(postData.userVote || null);

      // Trigger counter animation
      setTimeout(() => startCounterAnimation(), 100);
    } else if (linkId) {
      loadVoteData();
    }

    // Always load user vote if user is logged in
    if (linkId && user) {
      loadUserVote();
    }
  }, [linkId, postData, user, startCounterAnimation]);

  // Animate counter when vote stats change
  useEffect(() => {
    if (voteStats.total > 0) {
      startCounterAnimation();
    }
  }, [voteStats.total, startCounterAnimation]);

  const loadVoteData = async () => {
    try {
      setLoading(true);

      // Try to load from API
      const response = await fetch(`/api/votes/${linkId}/stats`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        const newStats = {
          safe: data.statistics?.safe || 0,
          unsafe: data.statistics?.unsafe || 0,
          suspicious: data.statistics?.suspicious || 0,
          total: data.statistics?.total || 0
        };
        setVoteStats(newStats);
        setUserVote(data.userVote);

        // Trigger counter animation
        setTimeout(() => startCounterAnimation(), 100);
      } else {
        // Fallback to mock data
        const safe = Math.floor(Math.random() * 50) + 10;
        const unsafe = Math.floor(Math.random() * 20);
        const suspicious = Math.floor(Math.random() * 10);
        setVoteStats({
          safe,
          unsafe,
          suspicious,
          total: safe + unsafe + suspicious
        });
      }
    } catch (error) {
      console.error('Load vote data error:', error);
      // Fallback to mock data
      const safe = Math.floor(Math.random() * 50) + 10;
      const unsafe = Math.floor(Math.random() * 20);
      const suspicious = Math.floor(Math.random() * 10);
      setVoteStats({
        safe,
        unsafe,
        suspicious,
        total: safe + unsafe + suspicious
      });
    } finally {
      setLoading(false);
    }
  };

  // Load user's vote for this link
  const loadUserVote = async () => {
    if (!linkId || !user) return;

    try {
      const token = localStorage.getItem('authToken') || localStorage.getItem('backendToken');
      const response = await fetch(`/api/votes/${linkId}/user`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setUserVote(data.userVote?.voteType || null);
      }
    } catch (error) {
      console.error('Error loading user vote:', error);
    }
  };

  const handleVote = async (voteType) => {
    if (!user) {
      alert('Vui lòng đăng nhập để vote');
      return;
    }

    try {
      setLoading(true);
      const isUnvote = userVote === voteType;
      const oldVote = userVote;

      // Add vote animation effect
      const buttonRef = voteType === 'safe' ? safeButtonRef :
                       voteType === 'suspicious' ? suspiciousButtonRef :
                       unsafeButtonRef;

      if (buttonRef.current) {
        gsap.to(buttonRef.current, {
          scale: 0.9,
          duration: 0.1,
          yoyo: true,
          repeat: 1,
          ease: "power2.inOut"
        });
      }

      // Optimistic update
      if (isUnvote) {
        // Remove vote
        setUserVote(null);
        setVoteStats(prev => ({
          ...prev,
          [voteType]: Math.max(0, prev[voteType] - 1),
          total: Math.max(0, prev.total - 1)
        }));
      } else {
        // Add/change vote
        setUserVote(voteType);
        setVoteStats(prev => {
          let newStats = { ...prev };

          // Remove old vote if exists
          if (oldVote) {
            newStats[oldVote] = Math.max(0, newStats[oldVote] - 1);
            newStats.total = Math.max(0, newStats.total - 1);
          }

          // Add new vote
          newStats[voteType] += 1;
          newStats.total += 1;

          return newStats;
        });
      }

      // Send to API
      let response;
      if (isUnvote) {
        // Delete vote
        response = await fetch(`/api/votes/${linkId}`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('authToken') || localStorage.getItem('backendToken')}`
          }
        });
      } else {
        // Submit new vote
        response = await fetch(`/api/votes/${linkId}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('authToken') || localStorage.getItem('backendToken')}`
          },
          body: JSON.stringify({ voteType })
        });
      }

      // Check if API call was successful
      if (!response.ok) {
        throw new Error('Vote API call failed');
      }

    } catch (error) {
      console.error('Vote error:', error);
      // Revert changes on error
      if (postData && postData.voteStats) {
        setVoteStats({
          safe: postData.voteStats.safe || 0,
          unsafe: postData.voteStats.unsafe || 0,
          suspicious: postData.voteStats.suspicious || 0,
          total: postData.voteStats.total || 0
        });
        setUserVote(postData.userVote || null);
      } else if (linkId) {
        await loadVoteData();
      }
    } finally {
      setLoading(false);
    }
  };

  const formatNumber = (num) => {
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}k`;
    }
    return num.toString();
  };

  if (loading && voteStats.total === 0) {
    return (
      <div className="flex flex-col items-center space-y-1 animate-pulse">
        <div className="w-8 h-6 bg-gray-200 rounded"></div>
        <div className="w-6 h-4 bg-gray-200 rounded"></div>
        <div className="w-8 h-6 bg-gray-200 rounded"></div>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center space-y-1 relative">
      {/* Safe Vote Button */}
      <button
        ref={safeButtonRef}
        onClick={() => handleVote('safe')}
        disabled={loading || !user}
        className={`p-1 rounded transition-colors ${
          userVote === 'safe'
            ? 'text-green-600 bg-green-100 dark:bg-green-900/30'
            : isDarkMode
            ? 'text-gray-400 hover:text-green-500'
            : 'text-gray-500 hover:text-green-500'
        } ${!user ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}`}
        title={!user ? 'Đăng nhập để vote' : 'Đáng tin cậy'}
      >
        <ChevronUp
          className={`w-5 h-5 ${userVote === 'safe' ? 'fill-current' : ''}`}
        />
      </button>

      {/* Vote Count */}
      <div
        ref={counterRef}
        className={`text-xs font-bold px-1 min-w-[24px] text-center ${
          isDarkMode ? 'text-gray-300' : 'text-gray-700'
        }`}
      >
        {formatNumber(voteStats.total)}
      </div>

      {/* Suspicious Vote Button */}
      <button
        ref={suspiciousButtonRef}
        onClick={() => handleVote('suspicious')}
        disabled={loading || !user}
        className={`p-1 rounded transition-colors ${
          userVote === 'suspicious'
            ? 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30'
            : isDarkMode
            ? 'text-gray-400 hover:text-yellow-500'
            : 'text-gray-500 hover:text-yellow-500'
        } ${!user ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}`}
        title={!user ? 'Đăng nhập để vote' : 'Nghi ngờ'}
      >
        <span className="text-sm">⚠</span>
      </button>

      {/* Unsafe Vote Button */}
      <button
        ref={unsafeButtonRef}
        onClick={() => handleVote('unsafe')}
        disabled={loading || !user}
        className={`p-1 rounded transition-colors ${
          userVote === 'unsafe'
            ? 'text-red-600 bg-red-100 dark:bg-red-900/30'
            : isDarkMode
            ? 'text-gray-400 hover:text-red-500'
            : 'text-gray-500 hover:text-red-500'
        } ${!user ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}`}
        title={!user ? 'Đăng nhập để vote' : 'Không đáng tin'}
      >
        <ChevronDown
          className={`w-5 h-5 ${userVote === 'unsafe' ? 'fill-current' : ''}`}
        />
      </button>

      {/* Loading indicator */}
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white/80 dark:bg-gray-800/80 rounded">
          <div
            ref={loadingRef}
            className="w-3 h-3 border-2 border-gray-300 border-t-blue-600 rounded-full"
            style={{ animation: 'spin 1s linear infinite' }}
          ></div>
        </div>
      )}
    </div>
  );
};

export default VoteComponent;
